import assert from "node:assert";

import {
  Account,
  AccountRepository,
  SignInConfirmationCode,
  WebAuthnCredential,
} from "../app/Authenticator.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresAccountRepository implements AccountRepository {
  constructor(private pool: ConnectionPool) {}

  async find(id: string): Promise<Account | null> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<RawAccount>(sql`
        SELECT
          id,
          slug,
          kinopoisk_id
        FROM
          account
        WHERE
          id = ${Number(id)}
      `);

      const webAuthnCredentials =
        await connection.query<RawAccountWebAuthnCredential>(sql`
        SELECT
          account_id,
          credential_id,
          credential_public_key,
          count,
          credential_device_type,
          credential_backed_up,
          transports,
          relying_party_id
        FROM
          account_webauthn_credential
        WHERE
          account_id = ${Number(id)}
      `);

      const signInConfirmationCodes =
        await connection.query<RawAccountSignInConfirmationCode>(sql`
        SELECT
          account_id,
          code,
          credential_id
        FROM
          account_sign_in_confirmation_code
        WHERE
          account_id = ${Number(id)}
      `);

      return rows[0]
        ? toAccount(rows[0], webAuthnCredentials, signInConfirmationCodes)
        : null;
    });
  }

  findByWebauthnCredentialId(
    credentialId: string,
  ): Promise<[Account, WebAuthnCredential] | [null, null]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<RawAccount>(sql`
        SELECT
          id,
          slug,
          kinopoisk_id
        FROM
          account
        JOIN
          account_webauthn_credential
          ON account_webauthn_credential.account_id = account.id
        WHERE
          account_webauthn_credential.credential_id = ${credentialId}
      `);

      if (!rows[0]) {
        return [null, null];
      }

      const webAuthnCredentials =
        await connection.query<RawAccountWebAuthnCredential>(sql`
        SELECT
          account_id,
          credential_id,
          credential_public_key,
          count,
          credential_device_type,
          credential_backed_up,
          transports,
          relying_party_id
        FROM
          account_webauthn_credential
        WHERE
          account_id = ${rows[0].id}
      `);

      const signInConfirmationCodes =
        await connection.query<RawAccountSignInConfirmationCode>(sql`
      SELECT
        account_id,
        code,
        credential_id
      FROM
        account_sign_in_confirmation_code
      WHERE
        account_id = ${rows[0].id}
    `);

      const account: Account = toAccount(
        rows[0],
        webAuthnCredentials,
        signInConfirmationCodes,
      );
      const credentials = account.webAuthnCredentials.find(
        (cr) => cr.credentialId === credentialId,
      );

      assert(credentials, "Expected credentials to exist");

      return [account, credentials];
    });
  }

  findBySignInConfirmationCode(
    confirmationCode: string,
  ): Promise<[Account, SignInConfirmationCode] | [null, null]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<RawAccount>(sql`
        SELECT
          id,
          slug,
          kinopoisk_id
        FROM
          account
        JOIN
          account_sign_in_confirmation_code
          ON account_sign_in_confirmation_code.account_id = account.id
        WHERE
          account_sign_in_confirmation_code.code = ${confirmationCode}
      `);

      if (!rows[0]) {
        return [null, null];
      }

      const webAuthnCredentials =
        await connection.query<RawAccountWebAuthnCredential>(sql`
        SELECT
          account_id,
          credential_id,
          credential_public_key,
          count,
          credential_device_type,
          credential_backed_up,
          transports,
          relying_party_id
        FROM
          account_webauthn_credential
        WHERE
          account_id = ${rows[0].id}
      `);

      const signInConfirmationCodes =
        await connection.query<RawAccountSignInConfirmationCode>(sql`
      SELECT
        account_id,
        code,
        credential_id
      FROM
        account_sign_in_confirmation_code
      WHERE
        account_id = ${rows[0].id}
    `);

      const account: Account = toAccount(
        rows[0],
        webAuthnCredentials,
        signInConfirmationCodes,
      );
      const code = account.signInConfirmationCodes.find(
        (c) => c.code === confirmationCode,
      );

      assert(code, "Expected confirmation code to exist");

      return [account, code];
    });
  }

  async findByKinopoiskIds(
    kinopoiskIds: string[],
  ): Promise<(Account | null)[]> {
    if (kinopoiskIds.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<RawAccount>(sql`
        SELECT
          id,
          slug,
          kinopoisk_id
        FROM
          account
        WHERE
          kinopoisk_id IN (${sql.raw(
            kinopoiskIds.map((id) => Number(id)).join(", "),
          )})
      `);

      if (rows.length === 0) {
        return [];
      }

      const webAuthnCredentials =
        await connection.query<RawAccountWebAuthnCredential>(sql`
        SELECT
          account_id,
          credential_id,
          credential_public_key,
          count,
          credential_device_type,
          credential_backed_up,
          transports,
          relying_party_id
        FROM
          account_webauthn_credential
        WHERE
          account_id IN (${sql.raw(rows.map((row) => row.id).join(", "))})
      `);

      const signInConfirmationCodes =
        await connection.query<RawAccountSignInConfirmationCode>(sql`
      SELECT
        account_id,
        code,
        credential_id
      FROM
        account_sign_in_confirmation_code
      WHERE
        account_id IN (${sql.raw(rows.map((row) => row.id).join(", "))})
    `);

      return kinopoiskIds
        .map(
          (kinopoiskId) =>
            rows.find((row) => row.kinopoisk_id === Number(kinopoiskId)) ??
            null,
        )
        .map((row) =>
          row !== null
            ? toAccount(
                row,
                webAuthnCredentials.filter(
                  (credential) => credential.account_id === row.id,
                ),
                signInConfirmationCodes.filter(
                  (confirmationCode) => confirmationCode.account_id === row.id,
                ),
              )
            : null,
        );
    });
  }

  async set(account: Account): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_account_webauthn_credential (LIKE account_webauthn_credential)`,
      );
      await connection.copyFrom(
        "COPY tmp_account_webauthn_credential (account_id, credential_id, credential_public_key, count, credential_device_type, credential_backed_up, transports, relying_party_id) FROM STDIN",
        account.webAuthnCredentials.map((credential) => ({
          accountId: Number(account.id),
          credential_id: credential.credentialId,
          credential_public_key: credential.credentialPublicKey,
          count: credential.counter,
          credential_device_type: credential.credentialDeviceType,
          credential_backed_up: credential.credentialBackedUp,
          transports: credential.transports,
          relying_party_id: credential.relyingPartyId,
        })),
      );
      await connection.query(
        sql`
          INSERT INTO account_webauthn_credential
          (SELECT * FROM tmp_account_webauthn_credential)
          ON CONFLICT (account_id, credential_id) DO NOTHING
        `,
      );
      await connection.query(sql`DROP TABLE tmp_account_webauthn_credential`);

      await connection.query(
        sql`CREATE TEMP TABLE tmp_account_sign_in_confirmation_code (LIKE account_sign_in_confirmation_code)`,
      );
      await connection.copyFrom(
        "COPY tmp_account_sign_in_confirmation_code (account_id, code, credential_id, created_at, updated_at) FROM STDIN",
        account.signInConfirmationCodes.map((code) => ({
          accountId: Number(account.id),
          code: code.code,
          credential_id: code.credentialId,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO account_sign_in_confirmation_code
          (SELECT * FROM tmp_account_sign_in_confirmation_code)
          ON CONFLICT ("code")
            DO UPDATE SET account_id = excluded.account_id,
                          credential_id = excluded.credential_id,
                          updated_at = excluded.updated_at
          WHERE (
            account_sign_in_confirmation_code.account_id,
            account_sign_in_confirmation_code.code,
            account_sign_in_confirmation_code.credential_id
          ) IS DISTINCT FROM (
            excluded.account_id,
            excluded.code,
            excluded.credential_id
          )
        `,
      );
      await connection.query(
        sql`DROP TABLE tmp_account_sign_in_confirmation_code`,
      );
    });
  }
}

function toAccount(
  rawAccount: RawAccount,
  rawWebAuthnCredentials: RawAccountWebAuthnCredential[],
  rawSignInConfirmationCodes: RawAccountSignInConfirmationCode[],
): Account {
  return {
    id: String(rawAccount.id),
    name: rawAccount.slug,
    kinopoiskId: String(rawAccount.kinopoisk_id),
    webAuthnCredentials: rawWebAuthnCredentials.map((credential) => ({
      credentialId: credential.credential_id,
      credentialPublicKey: credential.credential_public_key,
      counter: credential.count,
      credentialDeviceType: credential.credential_device_type,
      credentialBackedUp: credential.credential_backed_up,
      transports:
        (credential.transports as AuthenticatorTransport[] | undefined) ??
        undefined,
      relying_party_id: credential.relying_party_id ?? undefined,
    })),
    signInConfirmationCodes: rawSignInConfirmationCodes.map((c) =>
      c.credential_id
        ? {
            code: c.code,
            credentialId: c.credential_id,
            used: true,
          }
        : {
            code: c.code,
            credentialId: null,
            used: false,
          },
    ),
  };
}

interface RawAccount {
  id: number;
  slug: string;
  kinopoisk_id: number;
}

interface RawAccountWebAuthnCredential {
  account_id: number;
  credential_id: string;
  credential_public_key: string;
  count: number;
  credential_device_type: string;
  credential_backed_up: boolean;
  transports: string[] | null;
  relying_party_id: string | null;
}

interface RawAccountSignInConfirmationCode {
  account_id: number;
  code: string;
  credential_id: string | null;
}
